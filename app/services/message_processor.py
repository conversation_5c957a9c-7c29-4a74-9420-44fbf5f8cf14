from dataclasses import dataclass
from functools import cached_property
import logging
from typing import Any, Optional
from uuid import UUID

from fastapi import UploadFile

from constants.extracted_data import ConfirmedDataFields, ConversationState, DataSourceType, MissingDataStatus
from constants.message import ConversationMessageIntention, SuggestedUserPrompt, SystemReplyType
from exceptions import AIBadRequestError, EntityNotFoundError
from repositories import ConversationRepository
from schemas import (
    ClientCreateRequest,
    ClientSearchRequest,
    ConversationMessageIntentClassifierServiceResponse,
    ConversationMessageProcessingResult,
    DatePickerOption,
)
from schemas.confirmed_data import ConfirmedData
from schemas.conversation_message.message import UserMessageSerializer
from schemas.extracted_data import AggregatedData

from .date_validator import DateValidatorService
from .document import DocumentService
from .extracted_data import ExtractedDataService
from .intent_classifier import IntentClassifierService
from .mixins import CombinedHistoryMixin


logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class ExtractionProcessingResult:
    """Result of uncertainty intention processing."""

    system_reply: str
    system_reply_type: SystemReplyType
    missing_data_status: MissingDataStatus
    next_expected_field: str | None = None
    missing_fields: list[str] | None = None
    options: list[str] | list[tuple[str | None, str | None]] | None = None
    conversation_state: Optional[ConversationState] = None


@dataclass(frozen=True)
class ConversationMessageProcessor(CombinedHistoryMixin):
    """Processor for conversation message intention."""

    conversation_id: UUID
    user_message: UserMessageSerializer
    intent_classifier_service: IntentClassifierService
    extracted_data_service: ExtractedDataService
    conversation_repository: ConversationRepository
    date_validator_service: DateValidatorService
    document_service: DocumentService
    files: list[UploadFile] | None = None
    token: str = ''

    @cached_property
    def _cleaned_user_message(self) -> str:
        return self.user_message.content.strip()

    async def _get_intent(self) -> ConversationMessageIntention:
        """Intent getter."""

        ########################
        # Pre-processing before intent classification

        # Process empty user message
        if not self._cleaned_user_message:
            if self.files:
                return ConversationMessageIntention.EXTRACTION
            else:
                return ConversationMessageIntention.UNDEFINED

        # Process user message as suggested reply
        user_message_as_suggested_reply: SuggestedUserPrompt | None = self.user_message.as_suggested_reply
        if user_message_as_suggested_reply:
            if user_message_as_suggested_reply == SuggestedUserPrompt.NO_CREATE_NEW_QUAL:  # Case 1, 2
                return ConversationMessageIntention.DASH_DISCARD
            elif user_message_as_suggested_reply == SuggestedUserPrompt.SHOW_ME_AN_EXAMPLE_PROMPT:  # Case 3, 4, 5
                return ConversationMessageIntention.EXAMPLE
            elif user_message_as_suggested_reply == SuggestedUserPrompt.NO_CREATE_MY_QUAL:  # Case 6, 7
                return ConversationMessageIntention.GENERATE_QUAL
            elif user_message_as_suggested_reply == SuggestedUserPrompt.ENTER_A_NEW_CLIENT:  # Case 8, 9
                return ConversationMessageIntention.PROVIDE_CLIENT_NAME
            elif user_message_as_suggested_reply == SuggestedUserPrompt.YES_THIS_IS_CORRECT:  # Case 10, 11
                return ConversationMessageIntention.USER_CONFIRMATION
            elif user_message_as_suggested_reply == SuggestedUserPrompt.NO_I_WILL_ENTER_CLIENT_NAME:  # Case 12, 13
                return ConversationMessageIntention.PROVIDE_CLIENT_NAME
            elif user_message_as_suggested_reply == SuggestedUserPrompt.YES:  # Case 14, 15
                return ConversationMessageIntention.USER_CONFIRMATION

            # Welcome message suggested prompts
            elif user_message_as_suggested_reply == SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION:
                raise ValueError(
                    'This reply should be processed before _get_intent is called. Please validate the logic'
                )
            elif user_message_as_suggested_reply == SuggestedUserPrompt.UPLOAD_DOCUMENT:
                raise ValueError(
                    'This reply should be processed at the frontend before sending files. Please validate the logic'
                )
            elif user_message_as_suggested_reply == SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM:
                return ConversationMessageIntention.MANUAL_LDMF_INPUT
            else:
                raise NotImplementedError(f'Suggested user prompt {user_message_as_suggested_reply} not implemented')

        ########################
        # Intent classification
        try:
            intent_classification_response: ConversationMessageIntentClassifierServiceResponse = (
                await self.intent_classifier_service.classify_intent(
                    user_message=self._cleaned_user_message,
                    response_cls=ConversationMessageIntentClassifierServiceResponse,
                )
            )
            intention = intent_classification_response.intention
        except AIBadRequestError:
            intention = ConversationMessageIntention.UNDEFINED

        logger.info(f'Classified intention: {intention}')
        return intention

    async def run(self) -> ConversationMessageProcessingResult:
        """Process the intention."""
        aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)

        ########################
        # Pre-processing before intent handler run

        user_message_as_suggested_reply: SuggestedUserPrompt | None = self.user_message.as_suggested_reply
        if user_message_as_suggested_reply:
            if user_message_as_suggested_reply == SuggestedUserPrompt.WRITE_A_BRIEF_DESCRIPTION:
                reply_type = SystemReplyType.BRIEF_DESCRIPTION
                return ConversationMessageProcessingResult(
                    intention=ConversationMessageIntention.UNCERTAINTY,
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    data={},
                )

        user_enters_new_client = self._latest_system_message_type in [
            SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE,
            SystemReplyType.NEED_INFO_CLIENT_NAME,
        ]
        logger.info(f'User enters a new client: {user_enters_new_client}')

        user_confirms_single_client = (
            aggregated_data.is_client_name_complete and aggregated_data.client_name[0] == self._cleaned_user_message
        )
        logger.info(f'User confirms single client: {user_confirms_single_client}')

        user_confims_one_from_many = (
            aggregated_data.client_name and len(aggregated_data.client_name) > 1
        ) and self._cleaned_user_message in aggregated_data.client_name
        logger.info(f'User confrims one or many: {user_confirms_single_client}')

        if user_confirms_single_client or user_confims_one_from_many or user_enters_new_client:
            data = await self._handle_client_name_input(
                self._cleaned_user_message, called_from=ConversationMessageProcessor.run.__name__
            )
            return ConversationMessageProcessingResult(
                intention=ConversationMessageIntention.USER_CONFIRMATION,
                system_reply=data.system_reply,
                system_reply_type=data.system_reply_type,
                data=self.dataclass_to_dict(data),
            )

        intention = await self._get_intent()

        if intention == ConversationMessageIntention.UNDEFINED:
            data = self._process_undefined()
        elif intention == ConversationMessageIntention.GENERATE_QUAL:
            data = await self._generate_qual()
        elif intention == ConversationMessageIntention.EXTRACTION:
            data = await self._extract_data()
        elif intention == ConversationMessageIntention.EXAMPLE:
            data = self._example_help()
        elif intention == ConversationMessageIntention.DASH_DISCARD:
            data = await self._dash_discard()
        elif intention == ConversationMessageIntention.UNCERTAINTY:
            data = self._uncertainty(aggregated_data)
        elif intention == ConversationMessageIntention.NEED_CONTEXT:
            data = self._uncertainty(aggregated_data)
        elif intention == ConversationMessageIntention.USER_CONFIRMATION:
            data = await self._user_confirmation()
        elif intention == ConversationMessageIntention.CHANGE_ENGAGEMENT_DATES:
            data = await self._change_engagement_dates()
        elif intention == ConversationMessageIntention.PROVIDE_CLIENT_NAME:
            data = await self._provide_client_name()
        elif intention == ConversationMessageIntention.MANUAL_LDMF_INPUT:
            data = await self._manual_ldmf_input()
        else:
            raise NotImplementedError(f'Intent {intention} not implemented')

        ########################
        # Post-processing after intent handler run
        # If the data is an instance of UncertaintyProcessingResult, convert it to a dict
        # for compatibility with ConversationMessageProcessingResult
        if isinstance(data, ExtractionProcessingResult):
            system_reply = data.system_reply
            system_reply_type = data.system_reply_type
            conversation_state = data.conversation_state
            if conversation_state:
                await self.conversation_repository.update_state(self.conversation_id, conversation_state)

            # Convert dataclass to dict, excluding None values
            data_dict = self.dataclass_to_dict(data)
        else:
            system_reply = data.pop('system_reply')
            system_reply_type = data.pop('system_reply_type')
            data_dict = data

        return ConversationMessageProcessingResult(
            intention=intention,
            system_reply=system_reply,
            system_reply_type=system_reply_type,
            data=data_dict,
        )

    def dataclass_to_dict(self, data: ExtractionProcessingResult) -> dict[str, Any]:
        data_dict = {
            k: v for k, v in data.__dict__.items() if v is not None and k not in ('system_reply', 'system_reply_type')
        }
        return data_dict

    ########################
    # intention handlers

    def _process_undefined(self) -> dict[str, Any]:
        reply_type = SystemReplyType.UNDEFINED
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}

    async def _generate_qual(self) -> ExtractionProcessingResult:
        try:
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id,
                token=self.token,
                confirmed_data=confirmed_data,
            )
            # ask user for missing data
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=missing_data_response.reply_type or SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            # user provided all the data, generate a qual
            else:
                # TODO(TASK-???): Call a service to generate a qual
                # ...
                await self.conversation_repository.update_state(self.conversation_id, ConversationState.QUAL_CREATED)
                reply_type = SystemReplyType.READY_TO_GENERATE_QUAL_REPLY
                confirmation_message = reply_type.message_text.format(client_name=confirmed_data.client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.DATA_COMPLETE,
                )
        except Exception as e:
            logger.error('Exception in _generate_qual method for conversation %s: %s', self.conversation_id, e)
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _extract_data(self) -> ExtractionProcessingResult:
        # Note: Text prompt creation is now handled in the unified queue approach
        # in ConversationMessageService.create() method

        user_message = self._cleaned_user_message

        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            aggregated_data = await self.extracted_data_service.aggregate_data(self.conversation_id)

            client_name_was_confirmed = await self._handle_manual_input(
                conversation_id=self.conversation_id,
                conversation_state=ConversationState(conversation.State),
                aggregated_data=aggregated_data,
                message=user_message,
            )
            if client_name_was_confirmed:
                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text.format(client_name=user_message),
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                )

            state_is_initial = str(conversation.State) == str(ConversationState.INITIAL)
            if state_is_initial and user_message:
                reply_type = SystemReplyType.EMPTY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and user_message
            ):
                # Check if user is responding to client creation prompt
                if confirmed_data.proposed_client_name is not None:
                    return await self._handle_client_creation_response(
                        proposed_client_name=confirmed_data.proposed_client_name
                    )

                # Extract client name from user message and process it
                client_name = user_message
                return await self._handle_client_name_input(
                    client_name, called_from=ConversationMessageProcessor._extract_data.__name__
                )

            # Check if user is providing manual input for dates
            elif str(conversation.State) == ConversationState.COLLECTING_DATES.value and user_message:
                # Extract dates from user message and process it
                return await self._handle_dates_input(user_message)

            # Check if user is providing manual input for country
            elif (
                str(conversation.State) == ConversationState.COLLECTING_COUNTRY.value
                and confirmed_data.ldmf_country is None
                and user_message
            ):
                ldmf_country = user_message
                return await self._handle_country_input(ldmf_country)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OBJECTIVE.value
                and confirmed_data.objective_and_scope is None
                and user_message
            ):
                return await self._handle_objective_and_scope_input(user_message)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OUTCOMES.value
                and confirmed_data.outcomes is None
                and user_message
            ):
                return await self._handle_outcomes_input(user_message)

            # Handle user response when in DATA_COMPLETE state (after showing CONFIRMED_FIELDS_READY)
            elif str(conversation.State) == ConversationState.DATA_COMPLETE.value and user_message:
                return await self._handle_data_complete_response(user_message, confirmed_data)

            # Check what data is missing using the enhanced service
            missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                conversation_id=self.conversation_id,
                token=self.token,
                confirmed_data=confirmed_data,
            )

            # Update conversation state based on the response
            if missing_data_response.status == MissingDataStatus.MISSING_DATA:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                return ExtractionProcessingResult(
                    system_reply=missing_data_response.message or '',
                    system_reply_type=SystemReplyType.MISSING_REQUIRED_DATA,
                    missing_data_status=missing_data_response.status,
                    next_expected_field=missing_data_response.next_expected_field,
                    missing_fields=missing_data_response.missing_fields,
                    options=missing_data_response.options,
                    conversation_state=missing_data_response.conversation_state,
                )

            elif missing_data_response.status == MissingDataStatus.DATA_COMPLETE:
                await self.conversation_repository.update_state(
                    self.conversation_id, missing_data_response.conversation_state
                )
                reply_type = SystemReplyType.CONFIRMED_FIELDS_READY
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=missing_data_response.status,
                )

            else:  # error status
                logger.error(
                    'Error in missing data collection for conversation %s: %s',
                    self.conversation_id,
                    missing_data_response.message,
                )
                reply_type = SystemReplyType.BRIEF_DESCRIPTION
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.ERROR,
                )

        except Exception as e:
            logger.error('Exception in _extract_data method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _dash_discard(self) -> dict[str, Any]:
        await self.extracted_data_service.delete(self.conversation_id, DataSourceType.KX_DASH)
        reply_type = SystemReplyType.WELCOME_MESSAGE
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}

    def _example_help(self) -> dict[str, Any]:
        reply_type = SystemReplyType.EXAMPLE
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}

    def _uncertainty(self, aggregated_data: AggregatedData) -> dict[str, Any]:
        if aggregated_data.is_empty:
            """
            USE CASE 11 - NEED INFO:
            GIVEN the user navigates to the Prompt page
            AND the user doesn't provide a prompt containing the data for qual creation (bare minimum 5 fields)
            WHEN the user enters the text in input to provide some instruction (for example "Not sure what to do")
            VERIFY that the user can see the following message: "I’ll need some more details in order to create a draft qual. Could you start by telling me about the client and what services we delivered?"
            AND the AI will ask sequential questions to collect all 5 fields one by one: (User Story 2195552)
            """
            reply_type = SystemReplyType.NEED_INFO_INITIAL
        else:
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
        return {'system_reply': reply_type.message_text, 'system_reply_type': reply_type}

    ########################
    # utils for extraction & confirmation handlers

    async def _analyze_dates_message(self, message: str) -> tuple[DatePickerOption, bool]:
        """
        Process message content and determine system reply.

        Args:
            message: The user message to analyze

        Returns:
            Tuple with date option and sure flag
        """
        date_validator_response = await self.date_validator_service.find_dates(user_message=message)

        start_date = date_validator_response.date_1
        end_date = date_validator_response.date_2

        sure = True
        for date in (start_date, end_date):
            if (
                date
                and date.day <= 12
                and not (await self.date_validator_service.date_is_text(user_message=message, date=date.isoformat()))
            ):
                sure = False
                break

        # Sort dates if both are present
        if start_date and end_date and end_date < start_date:
            start_date, end_date = end_date, start_date

        return DatePickerOption(start_date=start_date, end_date=end_date), sure

    async def _handle_dates_input(self, user_message: str) -> ExtractionProcessingResult:
        """
        Handle dates input by analyzing the message and determining next steps.

        Args:
            message: The user message to analyze

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        date_option, sure = await self._analyze_dates_message(user_message)
        start_date = date_option.start_date.isoformat() if date_option.start_date else None
        end_date = date_option.end_date.isoformat() if date_option.end_date else None

        if start_date and end_date and sure:
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                field_value=(start_date, end_date),
                state=ConversationState.COLLECTING_OBJECTIVE,
            )
            reply_type = SystemReplyType.DATES_CONFIRMED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )
        else:
            await self.conversation_repository.update_state(self.conversation_id, ConversationState.COLLECTING_DATES)
            reply_type = SystemReplyType.DATES_AMBIGUOUS
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(start_date, end_date)],
            )

    async def _handle_client_name_input(self, client_name: str, called_from: str = '') -> ExtractionProcessingResult:
        """
        Handle client name input by searching the API and determining next steps.

        Args:
            client_name: The client name to process

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """

        try:
            # Search for the client name using the quals client repository
            search_request = ClientSearchRequest(contains=client_name, page_size=5, page_idx=0)
            search_result = await self.extracted_data_service.quals_clients_repository.search_clients(
                search_request, self.token
            )

            exact_match = any(client.name == client_name for client in search_result.clients)

            if search_result.clients and len(search_result.clients) == 1 or exact_match:
                # Exactly one match found - auto-confirm
                confirmed_client_name = search_result.clients[0].name
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=str(ConfirmedDataFields.CLIENT_NAME),
                    field_value=confirmed_client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )

                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                confirmation_message = reply_type.message_text.format(client_name=confirmed_client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif search_result.clients and len(search_result.clients) > 1:
                # as for 2370678 - should not ask confirmation, just save it.
                # Multiple matches found - ask for confirmation with the original name
                request = ClientCreateRequest(name=client_name)
                created_client = await self.extracted_data_service.quals_clients_repository.create_client(
                    request, self.token
                )
                if created_client.success:
                    await self.extracted_data_service.update_confirmed_data(
                        conversation_id=self.conversation_id,
                        field_name=str(ConfirmedDataFields.CLIENT_NAME),
                        field_value=client_name,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )
                    reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                    confirmation_message = reply_type.message_text.format(client_name=client_name)
                    return ExtractionProcessingResult(
                        system_reply=confirmation_message,
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                    )

                reply_type = SystemReplyType.CLIENT_CREATION_FAILED
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[],
                )

            else:
                # No matches found - ask if user wants to add as new client
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_CLIENT_NAME
                )

                # Store the proposed client name in confirmed_data for session persistence
                confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                confirmed_data.proposed_client_name = client_name
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=self.conversation_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )
                reply_type = SystemReplyType.CLIENT_NOT_FOUND
                confirmation_message = reply_type.message_text.format(client_name=client_name)
                logger.info(
                    '%s system_reply_type detected in %s', reply_type, {self._handle_client_name_input.__name__}
                )
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    options=[],
                )

        except Exception as e:
            logger.error('Error handling client name input for conversation %s: %s', self.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=str(ConfirmedDataFields.CLIENT_NAME),
                field_value=client_name,
                state=ConversationState.COLLECTING_COUNTRY,
            )

            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )

    async def _handle_client_creation_response(self, proposed_client_name: str) -> ExtractionProcessingResult:
        """
        Handle user response to client creation prompt.

        Args:
            proposed_client_name: The client name that was proposed for creation

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        user_message_str = self.user_message.content
        user_response = user_message_str.strip().lower()

        # Check if user confirms adding the new client

        if any(
            keyword in user_response for keyword in [SuggestedUserPrompt.YES.value.lower(), 'add', 'create', 'confirm']
        ):
            try:
                # Create the new client using the repository

                create_request = ClientCreateRequest(name=proposed_client_name)
                create_result = await self.extracted_data_service.quals_clients_repository.create_client(
                    create_request, self.token
                )

                if create_result.success:
                    # Update confirmed data with the new client name and clear proposed name
                    await self.extracted_data_service.update_confirmed_data(
                        conversation_id=self.conversation_id,
                        field_name=ConfirmedDataFields.CLIENT_NAME.value,
                        field_value=proposed_client_name,
                        state=ConversationState.COLLECTING_COUNTRY,
                    )

                    # Clear the proposed client name
                    confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
                    confirmed_data.proposed_client_name = None

                    state = (
                        ConversationState.DATA_COMPLETE
                        if confirmed_data.required_fields_are_complete
                        else ConversationState.COLLECTING_CLIENT_NAME
                    )
                    await self.conversation_repository.update_confirmed_data_and_state(
                        public_id=self.conversation_id,
                        confirmed_data=confirmed_data,
                        state=state,
                    )

                    reply_type = SystemReplyType.CLIENT_CREATION_CONFIRMED
                    confirmation_message = reply_type.message_text.format(client_name=proposed_client_name)
                    return ExtractionProcessingResult(
                        system_reply=confirmation_message,
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                    )
                else:
                    # Client creation failed
                    reply_type = SystemReplyType.CLIENT_CREATION_UNSUCCESSFUL
                    return ExtractionProcessingResult(
                        system_reply=reply_type.message_text.format(proposed_client_name=proposed_client_name),
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.MISSING_DATA,
                        conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                    )

            except Exception as e:
                logger.error(
                    'Error creating client %s for conversation %s: %s', proposed_client_name, self.conversation_id, e
                )
                reply_type = SystemReplyType.CLIENT_CREATION_FAILED
                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text.format(proposed_client_name=proposed_client_name),
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        # If the response contains a potential client name, treat it as a new client name
        else:
            reply_type = SystemReplyType.CLIENT_CREATION_UNSURE
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_CLIENT_NAME,
            )

    async def _handle_objective_and_scope_input(self, objective_and_scope: str) -> ExtractionProcessingResult:
        """
        Handle user input for objective and scope.

        Args:
            objective_and_scope: The user's input for objective and scope

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        # Update confirmed data with the new objective and scope
        await self.extracted_data_service.update_confirmed_data(
            conversation_id=self.conversation_id,
            field_name=ConfirmedDataFields.OBJECTIVE_AND_SCOPE.value,
            field_value=objective_and_scope,
            state=ConversationState.COLLECTING_OUTCOMES,
        )

        reply_type = SystemReplyType.OBJECTIVE_AND_SCOPE_CONFIRMED
        confirmation_message = reply_type.message_text.format(objective_and_scope=objective_and_scope)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    async def _handle_outcomes_input(self, outcomes: str) -> ExtractionProcessingResult:
        """
        Handle user input for outcomes.

        Args:
            outcomes: The user's input for outcomes

        Returns:
            UncertaintyProcessingResult with system reply and other data
        """
        # Update confirmed data with the new outcomes
        await self.extracted_data_service.update_confirmed_data(
            conversation_id=self.conversation_id,
            field_name=ConfirmedDataFields.OUTCOMES.value,
            field_value=outcomes,
            state=ConversationState.DATA_COMPLETE,
        )

        reply_type = SystemReplyType.OUTCOMES_CONFIRMED
        confirmation_message = reply_type.message_text.format(outcomes=outcomes)
        return ExtractionProcessingResult(
            system_reply=confirmation_message,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.DATA_COMPLETE,
        )

    async def _handle_country_input(self, ldmf_country: str) -> ExtractionProcessingResult:
        """
        Handle country input by searching the API and determining next steps.
        """
        try:
            if ldmf_country == SuggestedUserPrompt.NO_I_WILL_ENTER_LEAD_DELOITTE_MEMBER_FIRM:
                reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=[ldmf_country],
                )

            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                ldmf_country, self.token
            )

            if verified_countries and len(verified_countries) == 1:
                verified_country = verified_countries[0]
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=self.conversation_id,
                    field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                    field_value=verified_country,
                    state=ConversationState.COLLECTING_DATES,
                )
                reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
                confirmation_message = reply_type.message_text.format(ldmf_country=verified_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                )

            elif verified_countries and len(verified_countries) > 1:
                # Multiple matches found - ask for confirmation with the original name
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_COUNTRY
                )

                reply_type = SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS
                confirmation_message = reply_type.message_text
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=verified_countries,
                )

            else:
                # No matches found - ask for confirmation of the ldmf country
                await self.conversation_repository.update_state(
                    self.conversation_id, ConversationState.COLLECTING_COUNTRY
                )
                reply_type = SystemReplyType.LDMF_COUNTRY_SINGLE_CONFIRMATION
                confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                    options=[ldmf_country],
                )
        except Exception as e:
            logger.error('Error handling country input for conversation %s: %s', self.conversation_id, e)
            # Fallback to simple confirmation
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=self.conversation_id,
                field_name=ConfirmedDataFields.LDMF_COUNTRY.value,
                field_value=ldmf_country,
                state=ConversationState.COLLECTING_COUNTRY,
            )
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=ldmf_country)
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
            )

    async def _handle_data_complete_response(
        self, user_message: str, confirmed_data: ConfirmedData
    ) -> ExtractionProcessingResult:
        """
        Handle user response when all data is complete and user was asked if they want to add anything else.

        Args:
            user_message: The user's response message
            confirmed_data: The confirmed data containing client_name for formatting

        Returns:
            ExtractionProcessingResult with appropriate system reply
        """
        user_response = user_message.strip().lower()

        # Check if user wants to proceed with qual creation
        if any(
            keyword in user_response
            for keyword in [
                SuggestedUserPrompt.NO_CREATE_MY_QUAL.value.lower(),
                'no',
                'create',
                'generate',
                'proceed',
                'ready',
            ]
        ):
            # Update conversation state to ready for qual creation
            await self.conversation_repository.update_state(
                self.conversation_id, ConversationState.READY_FOR_QUAL_CREATION
            )

            # Format the message with client name from confirmed data
            client_name = confirmed_data.client_name or 'the client'
            reply_type = SystemReplyType.READY_TO_CREATE_DRAFT_QUAL
            formatted_message = reply_type.message_text.format(client_name=client_name)
            return ExtractionProcessingResult(
                system_reply=formatted_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )

        # If user wants to add more information, keep them in DATA_COMPLETE state
        # and ask what they'd like to add
        else:
            reply_type = SystemReplyType.ADDITIONAL_DATA_PROPOSAL
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.DATA_COMPLETE,
            )

    ############
    # handlers
    async def _user_confirmation(self) -> ExtractionProcessingResult:
        user_message = self._cleaned_user_message
        try:
            # Get current conversation state and confirmed data
            conversation = await self.conversation_repository.get(self.conversation_id)
            if not conversation:
                raise EntityNotFoundError('Conversation', str(self.conversation_id))

            confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
            aggregated = await self.extracted_data_service.aggregate_data(self.conversation_id)

            client_name_was_confirmed = await self._handle_manual_input(
                conversation_id=self.conversation_id,
                conversation_state=ConversationState(conversation.State),
                aggregated_data=aggregated,
                message=user_message,
            )
            if client_name_was_confirmed:
                if str(conversation.State) == str(ConversationState.DATA_COMPLETE):
                    reply_type = SystemReplyType.CONFIRMED_FIELDS_READY

                    return ExtractionProcessingResult(
                        system_reply=reply_type.message_text,
                        system_reply_type=reply_type,
                        missing_data_status=MissingDataStatus.DATA_COMPLETE,
                        conversation_state=ConversationState.READY_FOR_QUAL_CREATION,
                    )

                reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
                client_name = user_message
                if user_message == str(SuggestedUserPrompt.YES_THIS_IS_CORRECT):
                    client_name = aggregated.client_name[0]

                return ExtractionProcessingResult(
                    system_reply=reply_type.message_text.format(client_name=client_name),
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_COUNTRY,
                )
            # Check if user is providing manual input for client name
            if (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and aggregated.client_name
            ):
                # Check if user is responding to client creation prompt
                if confirmed_data.proposed_client_name is not None:
                    return await self._handle_client_creation_response(
                        proposed_client_name=confirmed_data.proposed_client_name
                    )

                client_name = aggregated.client_name[0]

                user_message_is_yes = user_message.strip().lower() == SuggestedUserPrompt.YES.value.lower()
                previous_message_is_client_not_found = (
                    self._latest_system_message_type == SystemReplyType.CLIENT_NOT_FOUND
                )
                if user_message_is_yes and previous_message_is_client_not_found:
                    # this is a unique scenario, that happens because for some reason confirmed_data.proposed_client_name is None,
                    # even though client_name was extracted from initial user prompt, and was detected as CLIENT_NOT_FOUND.
                    # So we can safely make this manual check of previous system reply and process current user's message "YES".
                    # TODO: should confirmed_data.proposed_client_name be updated here? or in some other place at the moment of data extraction?
                    return await self._handle_client_creation_response(
                        proposed_client_name=client_name,
                    )

                return await self._handle_client_name_input(
                    client_name, called_from=ConversationMessageProcessor._user_confirmation.__name__
                )

            elif (
                str(conversation.State) == ConversationState.COLLECTING_CLIENT_NAME.value
                and confirmed_data.client_name is None
                and not aggregated.client_name
                and confirmed_data.proposed_client_name
            ):
                # Confirm
                return await self._handle_client_creation_response(
                    proposed_client_name=confirmed_data.proposed_client_name
                )

            elif (
                str(conversation.State) == ConversationState.COLLECTING_COUNTRY.value
                and confirmed_data.ldmf_country is None
                and aggregated.ldmf_country[0]
            ):
                ldmf_country = aggregated.ldmf_country[0]
                return await self._handle_country_input(ldmf_country)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OBJECTIVE.value
                and confirmed_data.objective_and_scope is None
                and aggregated.objective_and_scope
            ):
                return await self._handle_objective_and_scope_input(aggregated.objective_and_scope)

            elif (
                str(conversation.State) == ConversationState.COLLECTING_OUTCOMES.value
                and confirmed_data.outcomes is None
                and aggregated.outcomes
            ):
                return await self._handle_outcomes_input(aggregated.outcomes)

            # Handle user response when in DATA_COMPLETE state (after showing CONFIRMED_FIELDS_READY)
            elif str(conversation.State) == ConversationState.DATA_COMPLETE.value and user_message:
                return await self._handle_data_complete_response(user_message, confirmed_data)

            reply_type = SystemReplyType.FIELD_SAVED
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.DATA_COMPLETE,
            )
        except Exception as e:
            logger.error('Exception in _extract_data method for conversation %s: %s', self.conversation_id, e)
            # Fallback to original behavior on any error
            reply_type = SystemReplyType.BRIEF_DESCRIPTION
            return ExtractionProcessingResult(
                system_reply=reply_type.message_text,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.ERROR,
            )

    async def _change_engagement_dates(self) -> ExtractionProcessingResult:
        user_message = self._cleaned_user_message

        conversation = await self.conversation_repository.get(self.conversation_id)
        if not conversation:
            raise EntityNotFoundError('Conversation', str(self.conversation_id))

        confirmed_data = await self.conversation_repository.get_confirmed_data(self.conversation_id)
        if not confirmed_data.date_intervals:
            await self.conversation_repository.update_state(self.conversation_id, ConversationState.COLLECTING_DATES)

        date_option, _ = await self._analyze_dates_message(user_message)
        analyzed_start_date = date_option.start_date.isoformat() if date_option.start_date else None
        analyzed_end_date = date_option.end_date.isoformat() if date_option.end_date else None

        if confirmed_data.date_intervals:
            start_date, end_date = confirmed_data.date_intervals
            if analyzed_start_date or analyzed_end_date:
                start_date = analyzed_start_date if analyzed_start_date else start_date
                end_date = analyzed_end_date if analyzed_end_date else end_date

                reply_type = SystemReplyType.DATES_AMBIGUOUS
                confirmation_message = reply_type.message_text
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_DATES,
                    options=[(start_date, end_date)],
                )
            else:
                reply_type = SystemReplyType.DATES_AMBIGUOUS
                confirmation_message = reply_type.message_text
                return ExtractionProcessingResult(
                    system_reply=confirmation_message,
                    system_reply_type=reply_type,
                    missing_data_status=MissingDataStatus.MISSING_DATA,
                    conversation_state=ConversationState.COLLECTING_DATES,
                    options=[(start_date, end_date)],
                )
        else:
            reply_type = SystemReplyType.DATES_AMBIGUOUS
            confirmation_message = reply_type.message_text
            return ExtractionProcessingResult(
                system_reply=confirmation_message,
                system_reply_type=reply_type,
                missing_data_status=MissingDataStatus.MISSING_DATA,
                conversation_state=ConversationState.COLLECTING_DATES,
                options=[(analyzed_start_date, analyzed_end_date)],
            )

    async def _handle_manual_input(
        self,
        conversation_id: UUID,
        conversation_state: ConversationState,
        aggregated_data: AggregatedData,
        message: str,
    ) -> bool:
        """
        Handle manual input from user. For now it only handles client name.

        Args:
            conversation_id: The conversation ID
            conversation_state: The current state of the conversation
            aggregated_data: The aggregated data from all sources
            message: The user's message

        Returns:
            True if client name was confirmed and state updated, False otherwise.
        """
        states_for_client_name = [
            ConversationState.COLLECTING_CLIENT_NAME,
            ConversationState.INITIAL,
        ]

        # Only proceed if we're in a state expecting client name confirmation
        if conversation_state not in states_for_client_name:
            return False

        # Check if the user's message matches any suggested client name or confirmation prompt
        for cl_name in aggregated_data.client_name:
            if message == cl_name or message == str(SuggestedUserPrompt.YES_THIS_IS_CORRECT):
                confirmed_client_name = cl_name if message == str(SuggestedUserPrompt.YES_THIS_IS_CORRECT) else message
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.CLIENT_NAME.value,
                    field_value=confirmed_client_name,
                    state=ConversationState.COLLECTING_COUNTRY,
                )
                return True

        return False

    async def _provide_client_name(self) -> ExtractionProcessingResult:
        reply_type = SystemReplyType.PROVIDE_CLIENT_NAME_UNSURE
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.MISSING_DATA,
        )

    async def _manual_ldmf_input(self) -> ExtractionProcessingResult:
        reply_type = SystemReplyType.EXTRACTED_LDMF_NOT_VALID
        return ExtractionProcessingResult(
            system_reply=reply_type.message_text,
            system_reply_type=reply_type,
            missing_data_status=MissingDataStatus.EXTRACTED_DATA_NOT_VALID,
            conversation_state=ConversationState.COLLECTING_COUNTRY,
        )
